The vulnerability exists in the recover_signer function where only the s component is validated while the r component is not bounds-checked: cryptography.rs:72-74

The r value is copied directly without validation: cryptography.rs:78

The signature is then passed to k256::ecdsa::Signature::from_slice() without ensuring r is in the valid range [1, n-1]: cryptography.rs:103

Critical Impact
This vulnerability has real security implications because recover_signer is used in critical validation contexts, specifically to verify Linea sequencer signatures: validators.rs:768-769

The recovered sequencer address is then compared against the expected official sequencer address: validators.rs:772-781

Conclusion
The attack scenario described is valid: an attacker can craft malformed signatures with r' = r + n that will pass the current validation but potentially recover to different public keys, potentially bypassing sequencer authentication. This could allow unauthorized proof submission and protocol manipulation.

This is a genuine security vulnerability that needs to be fixed by adding proper bounds checking for the r component.

Notes
The SECP256K1N_HALF constant is properly defined and used for s validation constants.rs:84-87 , but the protocol lacks equivalent validation for the r component. According to ECDSA standards, both r and s should be validated to be in the range [1, n-1] where n is the curve order.