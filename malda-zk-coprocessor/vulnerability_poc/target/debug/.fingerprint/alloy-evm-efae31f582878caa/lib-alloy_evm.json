{"rustc": 13226066032359371072, "features": "[\"default\", \"op\", \"op-alloy-consensus\", \"op-revm\", \"std\"]", "declared_features": "[\"default\", \"op\", \"op-alloy-consensus\", \"op-revm\", \"secp256k1\", \"std\"]", "target": 5176978098157706261, "profile": 6436667884522905088, "path": 8466379190618631504, "deps": [[156817798628101091, "revm", false, 6872305701114151355], [1236390738802735335, "alloy_sol_types", false, 3651748782085884243], [3524131406528303713, "alloy_eips", false, 10831058908256926678], [5988724083763663492, "alloy_primitives", false, 11984454542914676456], [6076877872687043960, "op_revm", false, 10247717619725767774], [7889122437962920738, "alloy_hardforks", false, 12276870854625695592], [10806645703491011684, "thiserror", false, 6237539356624216404], [11293676373856528358, "derive_more", false, 5582655070990950649], [11709604483720470746, "op_alloy_consensus", false, 10475168096454514974], [16980811431682948333, "alloy_consensus", false, 2371258938147735798], [18125022703902813197, "auto_impl", false, 12467752895867406000]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-evm-efae31f582878caa/dep-lib-alloy_evm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}